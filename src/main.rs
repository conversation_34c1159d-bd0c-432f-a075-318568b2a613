use anyhow::{Context, Result};
use futures::{StreamExt, SinkExt};
use solana_sdk::{
    instruction::CompiledInstruction,
    pubkey::Pubkey,
    system_program,
};
use sqlx::{PgPool, Row};
use tracing::{error, info, warn};
use yellowstone_grpc_client::GeyserGrpcClient;
use yellowstone_grpc_proto::prelude::*;


#[derive(Debug)]
struct TransferLog {
    signature: String,
    slot: u64,
    from_address: String,
    to_address: String,
    amount: f64,
}

struct SolanaMonitor {
    db_pool: PgPool,
}

impl SolanaMonitor {
    async fn new() -> Result<Self> {
        // 数据库连接
        let database_url = "********************************************************************";
        let db_pool = PgPool::connect(database_url)
            .await
            .context("Failed to connect to database")?;

        // 测试数据库连接
        Self::test_database_connection(&db_pool).await?;

        Ok(Self {
            db_pool,
        })
    }

    async fn test_database_connection(pool: &PgPool) -> Result<()> {
        info!("Testing database connection...");
        
        let row = sqlx::query("SELECT 1 as test")
            .fetch_one(pool)
            .await
            .context("Failed to execute test query")?;
        
        let test_value: i32 = row.get("test");
        if test_value == 1 {
            info!("✅ Database connection successful");
        }

        // 检查表是否存在
        let exists: bool = sqlx::query_scalar(
            "SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = 'wallet_transfer_logs'
            )"
        )
        .fetch_one(pool)
        .await
        .context("Failed to check table existence")?;

        if exists {
            info!("✅ wallet_transfer_logs table exists");
        } else {
            error!("❌ wallet_transfer_logs table does not exist");
            return Err(anyhow::anyhow!("Required table not found"));
        }

        Ok(())
    }

    async fn insert_transfer_log(&self, log: &TransferLog) -> Result<bool> {
        let mut tx = self.db_pool.begin().await?;

        // 插入到 wallet_transfer_logs 表
        let result = sqlx::query(
            "INSERT INTO wallet_transfer_logs (signature, slot, from_address, to_address, amount)
             VALUES ($1, $2, $3, $4, $5)
             ON CONFLICT DO NOTHING
             RETURNING id"
        )
        .bind(&log.signature)
        .bind(log.slot as i64)
        .bind(&log.from_address)
        .bind(&log.to_address)
        .bind(log.amount)
        .fetch_optional(&mut *tx)
        .await
        .context("Failed to insert transfer log")?;

        if let Some(row) = result {
            let id: i32 = row.get("id");

            // 同时插入到 wallet_second_filter 表
            sqlx::query(
                "INSERT INTO wallet_second_filter (signature, slot, from_address, to_address, amount, status)
                 VALUES ($1, $2, $3, $4, $5, 0)
                 ON CONFLICT DO NOTHING"
            )
            .bind(&log.signature)
            .bind(log.slot as i64)
            .bind(&log.from_address)
            .bind(&log.to_address)
            .bind(log.amount)
            .execute(&mut *tx)
            .await
            .context("Failed to insert to second filter")?;

            tx.commit().await?;
            info!("✅ Transfer log inserted with ID: {}", id);
            Ok(true)
        } else {
            tx.rollback().await?;
            Ok(false)
        }
    }

    async fn start_monitoring<T>(&mut self, mut grpc_client: GeyserGrpcClient<T>) -> Result<()>
    where
        T: yellowstone_grpc_client::Interceptor + Send + Sync + 'static,
    {
        info!("Starting Solana transaction monitoring...");

        // 创建订阅请求
        let mut subscribe_request = SubscribeRequest {
            commitment: Some(CommitmentLevel::Processed as i32),
            accounts_data_slice: vec![],
            ping: None,
            ..Default::default()
        };

        // 配置交易订阅
        subscribe_request.transactions.insert(
            "client".to_string(),
            SubscribeRequestFilterTransactions {
                vote: Some(false),
                failed: Some(false),
                signature: None,
                account_include: vec![],
                account_exclude: vec![],
                account_required: vec![],
            },
        );

        info!("Subscribing to Solana transactions...");
        let (mut subscribe_tx, mut stream) = grpc_client.subscribe().await?;

        subscribe_tx.send(subscribe_request).await?;
        info!("✅ Subscription successful, waiting for transaction data...");

        // 启动ping任务保持连接
        // 注意：由于subscribe_tx不支持clone，我们暂时跳过ping功能
        // 在生产环境中，可能需要重新设计这部分逻辑

        // 处理数据流
        while let Some(message) = stream.next().await {
            match message {
                Ok(update) => {
                    if let Err(e) = self.process_update(update).await {
                        error!("Failed to process update: {}", e);
                    }
                }
                Err(e) => {
                    error!("Stream error: {}", e);
                    return Err(anyhow::anyhow!("Stream error: {}", e));
                }
            }
        }

        warn!("Stream ended");
        Ok(())
    }

    async fn process_update(&self, update: SubscribeUpdate) -> Result<()> {
        if let Some(subscribe_update::UpdateOneof::Transaction(tx_update)) = update.update_oneof {
            self.process_transaction_update(tx_update).await?;
        }
        Ok(())
    }

    async fn process_transaction_update(&self, tx_update: SubscribeUpdateTransaction) -> Result<()> {
        // 暂时简化处理，只记录基本信息
        info!("Received transaction update for slot: {}", tx_update.slot);

        if let Some(transaction_info) = tx_update.transaction {
            let signature = bs58::encode(&transaction_info.signature).into_string();
            info!("Transaction signature: {}", signature);
        }

        Ok(())
    }

    async fn process_instruction(
        &self,
        instruction: &CompiledInstruction,
        account_keys: &[Pubkey],
        pre_balances: &[u64],
        post_balances: &[u64],
        signature: &str,
        slot: u64,
    ) -> Result<()> {
        // 检查是否是系统程序指令
        let program_id = account_keys.get(instruction.program_id_index as usize)
            .ok_or_else(|| anyhow::anyhow!("Invalid program_id_index"))?;

        if *program_id != system_program::id() {
            return Ok(());
        }

        // 检查是否是转账指令 (指令类型2)
        if instruction.data.is_empty() || instruction.data[0] != 2 {
            return Ok(());
        }

        // 手动解析转账指令数据
        if instruction.data.len() < 5 {
            return Ok(());
        }

        // 从指令数据中提取lamports (小端序，4字节后的8字节)
        let lamports_bytes: [u8; 8] = instruction.data[4..12].try_into()
            .map_err(|_| anyhow::anyhow!("Invalid lamports data"))?;
        let lamports = u64::from_le_bytes(lamports_bytes);

        if instruction.accounts.len() < 2 {
            return Ok(());
        }

        let from_index = instruction.accounts[0] as usize;
        let to_index = instruction.accounts[1] as usize;

        let from_pubkey = account_keys.get(from_index)
            .ok_or_else(|| anyhow::anyhow!("Invalid from_index"))?;
        let to_pubkey = account_keys.get(to_index)
            .ok_or_else(|| anyhow::anyhow!("Invalid to_index"))?;

        let from_address = from_pubkey.to_string();
        let to_address = to_pubkey.to_string();
        let sol_amount = lamports as f64 / 1_000_000_000.0;

        // 应用过滤条件
        if from_address == to_address {
            return Ok(());
        }
        if sol_amount < 0.1 || sol_amount > 100.0 {
            return Ok(());
        }

        info!("💸 SOL Transfer detected:");
        info!("  Slot: {}", slot);
        info!("  Signature: {}", signature);
        info!("  From: {}", from_address);
        info!("  To: {}", to_address);
        info!("  Amount: {:.9} SOL", sol_amount);

        // 检查是否为新钱包
        if let (Some(&pre_balance), Some(&post_balance)) = (
            pre_balances.get(to_index),
            post_balances.get(to_index),
        ) {
            let pre_sol = pre_balance as f64 / 1_000_000_000.0;
            let post_sol = post_balance as f64 / 1_000_000_000.0;

            // 如果交易前余额小于0.001 SOL，认为是新钱包
            if pre_sol < 0.001 {
                info!("✨ New wallet detected! Address: {}", to_address);
                info!("   Pre-balance: {:.9} SOL", pre_sol);
                info!("   Post-balance: {:.9} SOL", post_sol);
                info!("   Transfer amount: {:.9} SOL", sol_amount);

                let transfer_log = TransferLog {
                    signature: signature.to_string(),
                    slot,
                    from_address,
                    to_address: to_address.clone(),
                    amount: sol_amount,
                };

                if self.insert_transfer_log(&transfer_log).await? {
                    info!("✅ New wallet transaction recorded: {}", to_address);
                }
            } else {
                info!("ℹ️ Not a new wallet, skipping: {}", to_address);
            }
        }

        Ok(())
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    info!("Starting Solana gRPC Monitor");

    loop {
        match SolanaMonitor::new().await {
            Ok(mut monitor) => {
                info!("✅ Monitor initialized successfully");

                // 创建gRPC客户端
                let builder = match GeyserGrpcClient::build_from_shared("http://ny.vision-node.com:10000") {
                    Ok(builder) => builder,
                    Err(e) => {
                        error!("Failed to create gRPC builder: {}", e);
                        error!("Retrying in 10 seconds...");
                        tokio::time::sleep(tokio::time::Duration::from_secs(10)).await;
                        continue;
                    }
                };

                let grpc_client = match builder.connect().await {
                    Ok(client) => client,
                    Err(e) => {
                        error!("Failed to connect to gRPC server: {}", e);
                        error!("Retrying in 10 seconds...");
                        tokio::time::sleep(tokio::time::Duration::from_secs(10)).await;
                        continue;
                    }
                };

                if let Err(e) = monitor.start_monitoring(grpc_client).await {
                    error!("Monitor error: {}", e);
                    error!("Restarting in 5 seconds...");
                    tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                }
            }
            Err(e) => {
                error!("Failed to initialize monitor: {}", e);
                error!("Retrying in 10 seconds...");
                tokio::time::sleep(tokio::time::Duration::from_secs(10)).await;
            }
        }
    }
}
